// Sales Order component for MRP Dashboard
import { connectionManager } from '../../core/connection.js';

export class SalesOrderComponent {
  constructor(container) {
    this.container = container;
    this.salesOrders = [];
    this.filteredOrders = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'OrderNbr';
    this.sortDirection = 'desc';
    this.filterStatus = 'all';
    this.isLoading = true;
    this.dbName = 'salesOrderDb';
    this.storeName = 'salesOrders';
    this.settingsStoreName = 'appSettings';
    this.dataSource = null;
    this.lastSyncTime = null;
  }

  async init() {
    console.log("Initializing Sales Order component");

    this.isLoading = true;
    this.render();

    try {
      await this.initDatabase();

      const connectionStatus = connectionManager.getConnectionStatus();
      const isConnected = connectionStatus && connectionStatus.acumatica && connectionStatus.acumatica.isConnected;

      await this.loadData(isConnected);

      this.isLoading = false;
      this.render();
    } catch (error) {
      console.error("Error initializing sales orders:", error);
      this.isLoading = false;
      this.showError("Failed to initialize: " + error.message);
      this.render();
    }
  }

  async initDatabase() {
    return new Promise((resolve, reject) => {
      // Try to open the database without specifying version
      const checkRequest = indexedDB.open(this.dbName);

      checkRequest.onsuccess = (event) => {
        const db = event.target.result;
        let needsUpgrade = false;

        // Check if all required stores exist
        if (!db.objectStoreNames.contains(this.storeName)) {
          needsUpgrade = true;
        }
        if (!db.objectStoreNames.contains(this.settingsStoreName)) {
          needsUpgrade = true;
        }

        const currentVersion = db.version;
        db.close();

        if (!needsUpgrade) {
          // Database exists and has all needed stores
          console.log("Sales Order database already exists with correct schema, version:", currentVersion);
          resolve();
          return;
        }

        // Need to upgrade the database
        console.log("Need to upgrade sales order database schema");

        // Open with a new version number
        const request = indexedDB.open(this.dbName, currentVersion + 1);

        request.onerror = (event) => {
          console.error("Error opening IndexedDB:", event.target.error);
          reject(new Error("Could not open sales order database"));
        };

        request.onsuccess = (event) => {
          console.log("Successfully opened sales order database");
          resolve();
        };

        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          console.log("Upgrading sales order database schema to version", db.version);

          // Create object store for sales orders if it doesn't exist
          if (!db.objectStoreNames.contains(this.storeName)) {
            const store = db.createObjectStore(this.storeName, { keyPath: "id" });

            // Create indices with unique: false
            store.createIndex("OrderNbr", "OrderNbr", { unique: false });
            store.createIndex("Status", "Status", { unique: false });
            store.createIndex("OrderType", "OrderType", { unique: false });
            store.createIndex("OrderTotal", "OrderTotal", { unique: false });
            store.createIndex("ShippingDate", "ShippingDate", { unique: false });
            store.createIndex("CreatedDate", "CreatedDate", { unique: false });

            console.log("Created sales orders store");
          }

          // Create object store for app settings if it doesn't exist
          if (!db.objectStoreNames.contains(this.settingsStoreName)) {
            db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
            console.log("Created settings store");
          }

          console.log("Sales order database schema upgrade complete");
        };
      };

      checkRequest.onerror = (event) => {
        console.error("Error checking database:", event.target.error);

        // If we can't open the database at all, try creating it from scratch
        const freshRequest = indexedDB.open(this.dbName, 1);

        freshRequest.onerror = (event) => {
          console.error("Error creating new database:", event.target.error);
          reject(new Error("Could not create sales order database"));
        };

        freshRequest.onsuccess = (event) => {
          console.log("Successfully created fresh sales order database");
          resolve();
        };

        freshRequest.onupgradeneeded = (event) => {
          const db = event.target.result;
          console.log("Creating new sales order database schema");

          // Create object store for sales orders
          const store = db.createObjectStore(this.storeName, { keyPath: "id" });

          // Create indices
          store.createIndex("OrderNbr", "OrderNbr", { unique: false });
          store.createIndex("Status", "Status", { unique: false });
          store.createIndex("OrderType", "OrderType", { unique: false });
          store.createIndex("OrderTotal", "OrderTotal", { unique: false });
          store.createIndex("ShippingDate", "ShippingDate", { unique: false });
          store.createIndex("CreatedDate", "CreatedDate", { unique: false });

          // Create object store for app settings
          db.createObjectStore(this.settingsStoreName, { keyPath: "id" });

          console.log("Sales order database schema created");
        };
      };
    });
  }

  async loadData(forceRefresh = false) {
    try {
      this.isLoading = true;
      this.render();

      // Check connection status
      const connectionStatus = connectionManager.getConnectionStatus();
      console.log("Acumatica connection status:", connectionStatus.acumatica);

      // When forceRefresh is true, always try to fetch from Acumatica first if connected
      if (forceRefresh && connectionStatus.acumatica.isConnected) {
        console.log("Force refreshing - fetching latest sales order data from Acumatica");
        try {
          const result = await this.fetchAcumaticaSalesOrders(connectionStatus.acumatica.instance);

          if (result.success) {
            // Parse the data and store in IndexedDB
            console.log("Successfully fetched data from Acumatica");
            this.salesOrders = this.parseAcumaticaSalesOrders(result.data);
            console.log(`Parsed ${this.salesOrders.length} sales orders`);

            // Verify line items are properly parsed
            let totalLineItems = 0;
            this.salesOrders.forEach(order => {
              if (Array.isArray(order.LineItems)) {
                totalLineItems += order.LineItems.length;
              }
            });
            console.log(`Total line items: ${totalLineItems}`);

            // Update data source and sync time
            this.dataSource = 'acumatica';
            this.lastSyncTime = new Date().toLocaleString();

            // Store in IndexedDB for offline access
            await this.storeSalesOrdersInIndexedDB(this.salesOrders);

            // Also store the sync info in settings
            await this.saveSettings({
              lastSyncTime: this.lastSyncTime
            });

            console.log(`Stored ${this.salesOrders.length} sales orders in IndexedDB`);

            // Continue with filtering
            this.filteredOrders = [...this.salesOrders];
            this.calculateTotalPages();
            this.isLoading = false;
            this.render();
            return;
          } else {
            console.warn("Error refreshing from Acumatica:", result.error);
            // Fall through to IndexedDB fallback
          }
        } catch (fetchError) {
          console.error("Error refreshing sales orders from Acumatica:", fetchError);
          // Fall through to IndexedDB fallback
        }
      }

      // Try to get data from IndexedDB first
      console.log("Attempting to fetch data from IndexedDB...");
      this.salesOrders = await this.getSalesOrdersFromIndexedDB();

      // Verify line items in retrieved data
      if (this.salesOrders.length > 0) {
        let totalLineItems = 0;
        this.salesOrders.forEach(order => {
          if (Array.isArray(order.LineItems)) {
            totalLineItems += order.LineItems.length;
          }
        });
        console.log(`Retrieved from IndexedDB - Total line items: ${totalLineItems}`);
      }

      // Also try to get the last sync time
      const settings = await this.loadSettings();
      if (settings && settings.lastSyncTime) {
        this.lastSyncTime = settings.lastSyncTime;
      }

      // If we have data in IndexedDB, use it
      if (this.salesOrders.length > 0) {
        console.log(`Retrieved ${this.salesOrders.length} sales orders from IndexedDB`);
        this.dataSource = 'indexedDB';
        this.filteredOrders = [...this.salesOrders];
        this.calculateTotalPages();
        this.isLoading = false;
        this.render();
        return;
      }

      // If no data in IndexedDB and connected to Acumatica, try fetching from there
      if (connectionStatus.acumatica.isConnected && !forceRefresh) {
        console.log("No data in IndexedDB, trying to fetch from Acumatica");
        try {
          const result = await this.fetchAcumaticaSalesOrders(connectionStatus.acumatica.instance);

          if (result.success) {
            // Parse the data and store in IndexedDB
            this.salesOrders = this.parseAcumaticaSalesOrders(result.data);

            // Update data source and sync time
            this.dataSource = 'acumatica';
            this.lastSyncTime = new Date().toLocaleString();

            await this.storeSalesOrdersInIndexedDB(this.salesOrders);

            // Also store the sync info in settings
            await this.saveSettings({
              lastSyncTime: this.lastSyncTime
            });

            console.log(`Stored ${this.salesOrders.length} sales orders in IndexedDB`);
            this.filteredOrders = [...this.salesOrders];
            this.calculateTotalPages();
            this.isLoading = false;
            this.render();
            return;
          } else {
            console.warn("Error fetching from Acumatica:", result.error);
          }
        } catch (fetchError) {
          console.error("Error fetching sales orders from Acumatica:", fetchError);
        }
      }

      // If we reach here, we couldn't get data from either Acumatica or IndexedDB
      this.salesOrders = [];
      this.filteredOrders = [];
      this.dataSource = 'empty';
      this.calculateTotalPages();
      this.isLoading = false;
      this.render();
      this.showError("No sales order data available. Please connect to Acumatica to fetch data.");

    } catch (error) {
      console.error('Error loading sales order data:', error);
      this.salesOrders = [];
      this.filteredOrders = [];
      this.dataSource = 'empty';
      this.calculateTotalPages();
      this.isLoading = false;
      this.render();
      this.showError("Error loading sales order data: " + error.message);
    }
  }

  async fetchAcumaticaSalesOrders(instance) {
    try {
      if (!instance) {
        const connectionStatus = connectionManager.getConnectionStatus();
        if (!connectionStatus.acumatica.instance) {
          throw new Error('No Acumatica instance URL available. Please check connection.');
        }
        instance = connectionStatus.acumatica.instance;
      }

      // Use the exact URL as specified
      const apiUrl = `${instance}/entity/test/22.200.001/SalesOrdersGabby?$filter=(Status%20eq%20%27Open%27%20or%20Status%20eq%20%27On%20Hold%27%20or%20Status%20eq%20%27Back%20Order%27%20or%20Status%20eq%20%27Shipping%27)%20and%20OrderType%20eq%20%27SO%27&$expand=inventoryitem%2Finventorybom`;

      console.log("Fetching sales orders with exact URL:", apiUrl);

      // Get cookies for authentication
      let cookieString = '';
      if (chrome?.cookies?.getAll) {
        try {
          const url = new URL(instance);
          const cookies = await chrome.cookies.getAll({ domain: url.hostname });
          cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
          console.log("Retrieved cookies for authentication");
        } catch (cookieError) {
          console.warn("Could not retrieve cookies:", cookieError);
        }
      }

      // Make request with cookies through the connection manager
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...(cookieString ? { 'Cookie': cookieString } : {})
        },
        credentials: 'include'  // Include cookies for authentication
      });

      // Log the response status for debugging
      console.log("Acumatica API response status:", response.status);

      // Check response
      if (!response.ok) {
        if (response.status === 401) {
          // Update connection status in connection manager
          connectionManager.connections.acumatica.isConnected = false;
          if (chrome?.storage?.local) {
            await chrome.storage.local.set({ 'connections': connectionManager.connections });
          }
          throw new Error('Authentication failed. Please reconnect to Acumatica.');
        }

        // Get response text for better error diagnostics
        const responseText = await response.text();
        throw new Error(`Failed to fetch sales orders: ${response.status} ${response.statusText}. Details: ${responseText.substring(0, 200)}`);
      }

      // Parse response
      const data = await response.json();
      console.log(`Received ${data.length} sales orders from Acumatica API`);

      // Validate the data structure
      if (data.length > 0) {
        const firstItem = data[0];
        console.log("First item structure check:", {
          hasOrderNbr: !!firstItem.OrderNbr,
          hasInventoryItems: !!firstItem.inventoryitem,
          inventoryItemsCount: firstItem.inventoryitem ? firstItem.inventoryitem.length : 0
        });

        // Log the full first item to debug (limiting nested objects for readability)
        console.log("First item sample:", this.simplifyForLogging(firstItem));

        // Log the first inventory item if available
        if (firstItem.inventoryitem && firstItem.inventoryitem.length > 0) {
          const firstInventory = firstItem.inventoryitem[0];
          console.log("First inventory item:", {
            id: firstInventory.id,
            InventoryID: firstInventory.InventoryID?.value,
            LineDescription: firstInventory.LineDescription?.value,
            hasBOM: !!firstInventory.inventorybom,
            bomCount: firstInventory.inventorybom ? firstInventory.inventorybom.length : 0
          });
        }
      }

      return { success: true, data };
    } catch (error) {
      console.error("Error fetching sales orders from Acumatica:", error);
      return { success: false, error: error.message };
    }
  }

  // Helper method to simplify objects for logging
  simplifyForLogging(obj, depth = 0) {
    if (depth > 1) return "[Nested Object]";
    if (!obj || typeof obj !== 'object') return obj;

    const result = {};
    for (const key in obj) {
      if (Array.isArray(obj[key])) {
        result[key] = `[Array: ${obj[key].length} items]`;
        if (obj[key].length > 0) {
          result[`${key}[0]`] = this.simplifyForLogging(obj[key][0], depth + 1);
        }
      } else if (typeof obj[key] === 'object') {
        if (key === 'value') {
          result[key] = obj[key];
        } else {
          result[key] = this.simplifyForLogging(obj[key], depth + 1);
        }
      } else {
        result[key] = obj[key];
      }
    }
    return result;
  }

  parseAcumaticaSalesOrders(salesOrderData) {
    try {
      console.log("Parsing Acumatica sales order data, raw sample:", this.simplifyForLogging(salesOrderData[0]));

      return salesOrderData.map(order => {
        try {
          console.log("Processing order:", order.OrderNbr?.value);

          // Generate unique ID if needed
          const id = order.id || `so-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

          // Extract main sales order fields
          const orderNbr = order.OrderNbr?.value || '';
          const status = order.Status?.value || '';
          const orderType = order.OrderType?.value || '';
          const orderTotal = parseFloat(order.OrderTotal?.value || 0);
          const orderedQty = parseFloat(order.OrderedQty?.value || 0);

          // Parse dates - avoid Date objects for API dates
          const parseDate = dateStr => {
            if (!dateStr) return null;
            try {
              const rawDateString = dateStr.value || dateStr;

              let year, month, day;

              if (typeof rawDateString === 'string' && rawDateString.includes('T')) {
                const datePart = rawDateString.split('T')[0];
                [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
              }

              return {
                rawValue: rawDateString,
                year: year,
                month: month,
                day: day,
                isAcumaticaDate: true
              };
            } catch (e) {
              console.error("Error parsing date:", dateStr, e);
              return null;
            }
          };

          const createdDate = parseDate(order.CreatedDate?.value);
          const shippingDate = parseDate(order.ShippingDate?.value);

          // Process inventory items (line items)
          const lineItems = [];
          let totalLineAmount = 0;
          let totalBOMItems = 0;

          if (Array.isArray(order.inventoryitem) && order.inventoryitem.length > 0) {
            order.inventoryitem.forEach(item => {
              const quantity = parseFloat(item.Quantity?.value || 0);
              const unitPrice = parseFloat(item.UnitPrice?.value || 0);
              const extAmount = quantity * unitPrice;

              totalLineAmount += extAmount;

              // Process BOM items for this inventory item
              const bomItems = [];
              if (Array.isArray(item.inventorybom) && item.inventorybom.length > 0) {
                item.inventorybom.forEach(bom => {
                  // Extract ProductionNbr - it could be in different formats
                  let productionNbr = '';
                  if (bom.ProductionNbr) {
                    if (typeof bom.ProductionNbr === 'string') {
                      productionNbr = bom.ProductionNbr.trim();
                    } else if (bom.ProductionNbr.value) {
                      productionNbr = bom.ProductionNbr.value.trim();
                    }
                  }

                  const bomItem = {
                    id: bom.id || `bom-${id}-${Math.random().toString(36).substring(2, 10)}`,
                    ProductionNbr: productionNbr,
                    rowNumber: bom.rowNumber || 0,
                    note: bom.note || ''
                  };
                  bomItems.push(bomItem);

                  // Only count as BOM item if it has actual data
                  if (productionNbr || bom.note) {
                    totalBOMItems++;
                  }
                });
              }

              // Extract inventory ID - handle different formats
              let inventoryID = '';
              if (item.InventoryID) {
                if (typeof item.InventoryID === 'string') {
                  inventoryID = item.InventoryID.trim();
                } else if (item.InventoryID.value) {
                  inventoryID = item.InventoryID.value.trim();
                }
              }

              // If no inventory ID but has BOM with ProductionNbr, use that as identifier
              if (!inventoryID && bomItems.length > 0) {
                const productionNumbers = bomItems
                  .filter(bom => bom.ProductionNbr && bom.ProductionNbr.trim())
                  .map(bom => bom.ProductionNbr.trim());

                if (productionNumbers.length > 0) {
                  // Use the first production number as the identifier
                  inventoryID = productionNumbers[0];
                }
              }

              // If still no ID, use a generic identifier
              if (!inventoryID) {
                inventoryID = 'N/A';
              }

              // Check if the InventoryID we're using is actually a ProductionNbr
              const isProductionNumber = bomItems.some(bom => bom.ProductionNbr && bom.ProductionNbr.trim() === inventoryID);

              const lineItem = {
                id: item.id || `line-${id}-${Math.random().toString(36).substring(2, 10)}`,
                rowNumber: item.rowNumber || 0,
                InventoryID: inventoryID,
                IsProductionNumber: isProductionNumber,
                OriginalInventoryID: item.InventoryID?.value || '',
                LineDescription: item.LineDescription?.value || '',
                Quantity: quantity,
                UnitPrice: unitPrice,
                UOM: item.UOM?.value || '',
                ExtAmount: extAmount,
                note: item.note?.value || '',
                BOMItems: bomItems,
                BOMCount: bomItems.length,
                HasProduction: bomItems.some(bom => bom.ProductionNbr && bom.ProductionNbr.trim())
              };

              lineItems.push(lineItem);
            });
          }

          // Build the sales order object with all available data
          const result = {
            id,
            OrderNbr: orderNbr,
            Status: status,
            OrderType: orderType,
            OrderTotal: orderTotal,
            OrderedQty: orderedQty,
            LineTotal: totalLineAmount,
            CreatedDate: createdDate,
            ShippingDate: shippingDate,
            LineItems: lineItems,
            LineItemCount: lineItems.length,
            TotalBOMItems: totalBOMItems,
            HasProductionOrders: lineItems.some(item => item.HasProduction)
          };

          // Validate result before returning
          if (!Array.isArray(result.LineItems)) {
            console.error(`LineItems is not an array for ${orderNbr}`, result.LineItems);
            result.LineItems = [];
            result.LineItemCount = 0;
          }

          // Log the final object structure for debugging
          console.log("Final sales order structure for", orderNbr, ":", {
            id: result.id,
            LineItemCount: result.LineItemCount,
            TotalBOMItems: result.TotalBOMItems,
            HasProductionOrders: result.HasProductionOrders
          });

          return result;
        } catch (orderError) {
          console.error("Error parsing individual sales order:", orderError, order);
          return null;
        }
      }).filter(order => order !== null); // Filter out any orders that failed to parse
    } catch (error) {
      console.error("Error parsing Acumatica sales orders:", error);
      return [];
    }
  }

  async getSalesOrdersFromIndexedDB() {
    return new Promise((resolve, reject) => {
      // Open without specifying version to use existing version
      const request = indexedDB.open(this.dbName);

      request.onerror = (event) => {
        console.error("Error opening database:", event.target.error);
        reject(new Error("Could not open sales order database"));
      };

      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for reading, version:", db.version);

        // Check if the store exists
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          resolve([]);
          return;
        }

        const transaction = db.transaction([this.storeName], "readonly");
        const store = transaction.objectStore(this.storeName);
        const getAllRequest = store.getAll();

        getAllRequest.onsuccess = () => {
          const salesOrders = getAllRequest.result;
          console.log(`Retrieved ${salesOrders.length} sales orders from IndexedDB`);
          resolve(salesOrders);
        };

        getAllRequest.onerror = (event) => {
          console.error("Error retrieving sales orders:", event.target.error);
          reject(new Error("Failed to retrieve sales orders from database"));
        };

        transaction.oncomplete = () => {
          db.close();
        };

        transaction.onerror = (event) => {
          console.error("Transaction error retrieving sales orders:", event.target.error);
          db.close();
        };
      };

      request.onupgradeneeded = (event) => {
        // This shouldn't happen when opening an existing database without specifying version
        console.warn("Unexpected database upgrade needed during read operation");
        event.target.transaction.abort();
        reject(new Error("Database schema needs upgrade, please reload the page"));
      };
    });
  }

  async storeSalesOrdersInIndexedDB(salesOrders) {
    return new Promise((resolve, reject) => {
      try {
        // Open database
        const request = indexedDB.open(this.dbName);

        request.onerror = (event) => {
          console.error("Error opening database for storing:", event.target.error);
          reject(new Error("Could not open sales order database for storing"));
        };

        request.onsuccess = (event) => {
          const db = event.target.result;
          console.log("Successfully opened database for writing, version:", db.version);

          // Check if the store exists
          if (!db.objectStoreNames.contains(this.storeName)) {
            db.close();
            reject(new Error("Sales order store not found for storing"));
            return;
          }

          try {
            // Verify we have data to store
            console.log(`Preparing to store ${salesOrders.length} sales orders`);

            // Debug log some of the orders
            if (salesOrders.length > 0) {
              const firstOrder = salesOrders[0];
              console.log("First order to store:", {
                id: firstOrder.id,
                OrderNbr: firstOrder.OrderNbr,
                LineItemCount: firstOrder.LineItems ? firstOrder.LineItems.length : 0,
                TotalBOMItems: firstOrder.TotalBOMItems || 0
              });

              // Check if LineItems are arrays
              console.log("LineItems is array:", Array.isArray(firstOrder.LineItems));

              // Deep clone each order to ensure all properties are properly stored
              const clonedOrders = salesOrders.map(order => {
                // Create a deep clone
                const clone = JSON.parse(JSON.stringify(order));

                // Ensure LineItems are arrays
                if (!Array.isArray(clone.LineItems)) {
                  console.warn(`Order ${clone.OrderNbr} has invalid LineItems - fixing`);
                  clone.LineItems = [];
                }

                // Update counts to match arrays
                clone.LineItemCount = clone.LineItems.length;

                return clone;
              });

              // Start transaction
              const transaction = db.transaction([this.storeName], "readwrite");
              const store = transaction.objectStore(this.storeName);

              // Set up transaction event handlers
              transaction.oncomplete = () => {
                console.log("Transaction completed successfully");
                db.close();
                resolve(true);
              };

              transaction.onerror = (event) => {
                console.error("Transaction error:", event.target.error);
                db.close();
                reject(new Error("Failed to store sales order data: " + event.target.error.message));
              };

              // Clear existing data
              const clearRequest = store.clear();

              clearRequest.onsuccess = () => {
                console.log(`Cleared existing data. Storing ${clonedOrders.length} orders.`);

                // If no orders to store, we're done
                if (clonedOrders.length === 0) {
                  db.close();
                  resolve(true);
                  return;
                }

                // Counter for completed operations
                let completedCount = 0;
                const totalCount = clonedOrders.length;

                // Process each order
                clonedOrders.forEach((order) => {
                  try {
                    // Log order before storing
                    console.log(`Storing order ${order.OrderNbr} with ${order.LineItemCount} line items`);

                    const putRequest = store.put(order);

                    putRequest.onsuccess = () => {
                      completedCount++;
                      if (completedCount === totalCount) {
                        console.log(`Successfully stored all ${completedCount} orders`);
                      }
                    };

                    putRequest.onerror = (event) => {
                      console.error(`Error storing order ${order.OrderNbr}:`, event.target.error);
                      completedCount++;
                      event.stopPropagation(); // Prevent transaction abort

                      if (completedCount === totalCount) {
                        console.log(`Completed storage operations with some errors`);
                      }
                    };
                  } catch (putError) {
                    console.error("Error in put operation:", putError);
                    completedCount++;

                    if (completedCount === totalCount) {
                      console.log(`Completed storage operations with some errors`);
                    }
                  }
                });
              };

              clearRequest.onerror = (event) => {
                console.error("Error clearing store:", event.target.error);
                db.close();
                reject(new Error("Failed to clear existing data"));
              };
            } else {
              console.warn("No sales orders to store");
              db.close();
              resolve(true);
            }
          } catch (transactionError) {
            console.error("Error setting up transaction:", transactionError);
            db.close();
            reject(new Error("Failed to set up database transaction"));
          }
        };

      } catch (error) {
        console.error("Unexpected error in storeSalesOrdersInIndexedDB:", error);
        reject(new Error("Unexpected error storing sales order data"));
      }
    });
  }

  async saveSettings(settings) {
    try {
      // Open database
      const request = indexedDB.open(this.dbName);

      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for saving settings:", event.target.error);
          reject(new Error("Could not open database for saving settings"));
        };
      });

      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.error("Settings store not found, cannot save settings");
        db.close();
        return;
      }

      const transaction = db.transaction([this.settingsStoreName], "readwrite");
      const store = transaction.objectStore(this.settingsStoreName);

      // Save sales order settings
      store.put({
        id: "salesOrderSettings",
        ...settings
      });

      await new Promise((resolve, reject) => {
        transaction.oncomplete = () => {
          resolve();
        };
        transaction.onerror = (event) => {
          reject(event.target.error);
        };
      });

      db.close();
      return true;
    } catch (error) {
      console.error("Error saving settings:", error);
      return false;
    }
  }

  async loadSettings() {
    try {
      // Open database
      const request = indexedDB.open(this.dbName);

      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for loading settings:", event.target.error);
          reject(new Error("Could not open database for loading settings"));
        };
      });

      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.log("Settings store not found, using defaults");
        db.close();
        return null;
      }

      const transaction = db.transaction([this.settingsStoreName], "readonly");
      const store = transaction.objectStore(this.settingsStoreName);

      // Get sales order settings
      const salesOrderSettings = await new Promise((resolve) => {
        const request = store.get("salesOrderSettings");
        request.onsuccess = () => resolve(request.result);
        request.onerror = (event) => {
          console.error("Error reading sales order settings:", event.target.error);
          resolve(null);
        };
      });

      // Apply settings if they exist
      if (salesOrderSettings) {
        if (salesOrderSettings.itemsPerPage) {
          this.itemsPerPage = salesOrderSettings.itemsPerPage;
        }

        if (salesOrderSettings.sortField) {
          this.sortField = salesOrderSettings.sortField;
        }

        if (salesOrderSettings.sortDirection) {
          this.sortDirection = salesOrderSettings.sortDirection;
        }

        if (salesOrderSettings.lastSyncTime) {
          this.lastSyncTime = salesOrderSettings.lastSyncTime;
        }
      }

      db.close();
      return salesOrderSettings;
    } catch (error) {
      console.error("Error loading settings:", error);
      return null;
    }
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredOrders.length / this.itemsPerPage));

    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  applyFilters() {
    let filtered = [...this.salesOrders];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(order => {
        return (
          order.OrderNbr.toLowerCase().includes(searchLower) ||
          order.Status.toLowerCase().includes(searchLower) ||
          order.OrderType.toLowerCase().includes(searchLower) ||
          order.LineItems.some(item =>
            item.LineDescription.toLowerCase().includes(searchLower) ||
            item.InventoryID.toLowerCase().includes(searchLower)
          )
        );
      });
    }

    // Apply status filter
    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(order => order.Status === this.filterStatus);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (this.sortField) {
        case 'OrderNbr':
          aValue = a.OrderNbr;
          bValue = b.OrderNbr;
          break;
        case 'Status':
          aValue = a.Status;
          bValue = b.Status;
          break;
        case 'OrderTotal':
          aValue = a.OrderTotal;
          bValue = b.OrderTotal;
          break;
        case 'OrderedQty':
          aValue = a.OrderedQty;
          bValue = b.OrderedQty;
          break;
        case 'LineItemCount':
          aValue = a.LineItemCount;
          bValue = b.LineItemCount;
          break;
        case 'CreatedDate':
          // Handle date objects
          if (a.CreatedDate && a.CreatedDate.year) {
            aValue = new Date(a.CreatedDate.year, a.CreatedDate.month - 1, a.CreatedDate.day);
          } else {
            aValue = new Date(0);
          }
          if (b.CreatedDate && b.CreatedDate.year) {
            bValue = new Date(b.CreatedDate.year, b.CreatedDate.month - 1, b.CreatedDate.day);
          } else {
            bValue = new Date(0);
          }
          break;
        case 'ShippingDate':
          // Handle date objects
          if (a.ShippingDate && a.ShippingDate.year) {
            aValue = new Date(a.ShippingDate.year, a.ShippingDate.month - 1, a.ShippingDate.day);
          } else {
            aValue = new Date(0);
          }
          if (b.ShippingDate && b.ShippingDate.year) {
            bValue = new Date(b.ShippingDate.year, b.ShippingDate.month - 1, b.ShippingDate.day);
          } else {
            bValue = new Date(0);
          }
          break;
        default:
          aValue = a[this.sortField] || '';
          bValue = b[this.sortField] || '';
      }

      // Handle different data types
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      } else if (aValue instanceof Date && bValue instanceof Date) {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      } else {
        // String comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        if (this.sortDirection === 'asc') {
          return aStr.localeCompare(bStr);
        } else {
          return bStr.localeCompare(aStr);
        }
      }
    });

    this.filteredOrders = filtered;
    this.calculateTotalPages();
    this.currentPage = 1; // Reset to first page when filtering
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading sales order data...</p>
      </div>
    `;
  }

  renderContent() {
    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        ${this.renderDataSourceNotice()}
        <div class="flex flex-col md:flex-row justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">Sales Orders</h2>

          <div class="flex flex-wrap items-center gap-2">
            <div class="relative">
              <input
                type="text"
                id="sales-search"
                placeholder="Search sales orders..."
                class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
                value="${this.searchTerm || ''}"
              >
              <button id="clear-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <select id="order-status-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
              <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Orders</option>
              <option value="Open" ${this.filterStatus === 'Open' ? 'selected' : ''}>Open</option>
              <option value="On Hold" ${this.filterStatus === 'On Hold' ? 'selected' : ''}>On Hold</option>
              <option value="Back Order" ${this.filterStatus === 'Back Order' ? 'selected' : ''}>Back Order</option>
              <option value="Shipping" ${this.filterStatus === 'Shipping' ? 'selected' : ''}>Shipping</option>
            </select>

            <div class="flex gap-2">
              <!-- Sales Orders Button -->
              <button id="sales-button" class="p-2 bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 rounded-md flex items-center" title="Sales Orders">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M8 11v6h8v-6M8 11H6a2 2 0 00-2 2v6a2 2 0 002 2h12a2 2 0 002-2v-6a2 2 0 00-2-2h-2"></path>
                </svg>
                <span class="ml-1 text-sm font-medium">Sales Orders</span>
              </button>

              <!-- Refresh Button -->
              <button id="refresh-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Refresh Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>

              <!-- Export Button -->
              <button id="export-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Export Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
              </button>

              <!-- Settings Button -->
              <button id="settings-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Settings">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div id="sales-table-container">
          <!-- Sales Orders Table -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OrderNbr">
                    Order # <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Status">
                    Status <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OrderTotal">
                    Total <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OrderedQty">
                    Qty <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="LineItemCount">
                    Items <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="CreatedDate">
                    Created <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ShippingDate">
                    Ship Date <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${this.renderTableRows()}
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
            <div class="text-sm text-gray-500 dark:text-gray-400">
              Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredOrders.length)} to
              ${Math.min(this.currentPage * this.itemsPerPage, this.filteredOrders.length)} of
              ${this.filteredOrders.length} results
            </div>

            <div class="flex items-center space-x-1">
              <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-double-left"></i>
              </button>
              <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-left"></i>
              </button>

              <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
                ${this.currentPage} of ${this.totalPages}
              </span>

              <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-right"></i>
              </button>
              <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-double-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    this.setupEventListeners();
  }

  renderDataSourceNotice() {
    if (!this.dataSource || this.salesOrders.length === 0) {
      return `
        <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4 dark:bg-blue-900 dark:border-blue-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-blue-500 dark:text-blue-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-blue-800 dark:text-blue-200">
                No sales order data available. Connect to Acumatica and click the refresh button to load sales orders.
              </p>
            </div>
          </div>
        </div>
      `;
    }

    const sourceText = this.dataSource === 'acumatica' ? 'Live data from Acumatica' : 'Cached data (offline)';
    const syncText = this.lastSyncTime ? ` • Last sync: ${this.lastSyncTime}` : '';

    return `
      <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-4 dark:bg-green-900 dark:border-green-600">
        <div class="flex items-center">
          <div class="flex-shrink-0 text-green-500 dark:text-green-400">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-green-800 dark:text-green-200">
              ${sourceText}${syncText} • ${this.salesOrders.length} sales orders loaded
            </p>
          </div>
        </div>
      </div>
    `;
  }

  renderTableRows() {
    if (this.filteredOrders.length === 0) {
      return `
        <tr>
          <td colspan="8" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
            <div class="flex flex-col items-center">
              <svg class="w-12 h-12 mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M8 11v6h8v-6M8 11H6a2 2 0 00-2 2v6a2 2 0 002 2h12a2 2 0 002-2v-6a2 2 0 00-2-2h-2"></path>
              </svg>
              <p class="text-lg font-medium mb-2">No sales orders found</p>
              <p class="text-sm">
                ${this.searchTerm || this.filterStatus !== 'all'
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Connect to Acumatica to load sales order data.'}
              </p>
            </div>
          </td>
        </tr>
      `;
    }

    // Calculate pagination
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredOrders.length);
    const pageOrders = this.filteredOrders.slice(startIndex, endIndex);

    return pageOrders.map(order => {
      // Check if any line items have production numbers
      const hasProductionOrders = order.LineItems && order.LineItems.some(item =>
        item.BOMItems && item.BOMItems.some(bom => bom.ProductionNbr && bom.ProductionNbr.trim())
      );

      return `
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
          <td class="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
            ${this.escapeHtml(order.OrderNbr)}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm">
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(order.Status)}">
              ${this.escapeHtml(order.Status)}
            </span>
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white text-right">
            ${this.formatCurrency(order.OrderTotal)}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white text-right">
            ${order.OrderedQty ? order.OrderedQty.toFixed(2) : '0.00'}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white text-center">
            <div class="flex items-center justify-center space-x-2">
              <span>${order.LineItemCount || 0}</span>
              ${hasProductionOrders ? `
                <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-200" title="Has Production Orders">
                  <i class="fas fa-cogs mr-1"></i>
                  BOM
                </span>
              ` : ''}
            </div>
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
            ${this.formatDate(order.CreatedDate)}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
            ${this.formatDate(order.ShippingDate)}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
            <button class="view-order text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" data-id="${order.id}">
              <i class="fas fa-eye mr-1"></i>
              View
            </button>
          </td>
        </tr>
      `;
    }).join('');
  }

  renderHeader() {
    const dataSourceInfo = this.getDataSourceInfo();

    return `
      <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <div class="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <i class="fas fa-shopping-cart text-blue-600 dark:text-blue-400"></i>
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Sales Orders</h2>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              ${this.filteredOrders.length} orders • ${dataSourceInfo}
            </p>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <button id="refreshSalesOrders" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <i class="fas fa-sync-alt mr-2"></i>
            Refresh
          </button>

          <button id="exportSalesOrders" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <i class="fas fa-download mr-2"></i>
            Export
          </button>

          <button id="salesOrderSettings" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <i class="fas fa-cog mr-2"></i>
            Settings
          </button>
        </div>
      </div>
    `;
  }

  renderFilters() {
    // Get unique statuses for filter dropdown
    const uniqueStatuses = [...new Set(this.salesOrders.map(order => order.Status))].sort();

    return `
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <div class="relative">
              <input
                type="text"
                id="salesOrderSearch"
                placeholder="Search orders, items, descriptions..."
                value="${this.escapeHtml(this.searchTerm)}"
                class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
              </div>
              ${this.searchTerm ? `
                <button id="clearSearch" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <i class="fas fa-times text-gray-400 hover:text-gray-600"></i>
                </button>
              ` : ''}
            </div>
          </div>

          <div class="sm:w-48">
            <select
              id="statusFilter"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Statuses</option>
              ${uniqueStatuses.map(status => `
                <option value="${this.escapeHtml(status)}" ${this.filterStatus === status ? 'selected' : ''}>
                  ${this.escapeHtml(status)}
                </option>
              `).join('')}
            </select>
          </div>
        </div>
      </div>
    `;
  }

  renderTable() {
    if (this.filteredOrders.length === 0) {
      return `
        <div class="p-8 text-center">
          <div class="text-gray-400 dark:text-gray-500 mb-4">
            <i class="fas fa-shopping-cart text-4xl"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Sales Orders Found</h3>
          <p class="text-gray-500 dark:text-gray-400">
            ${this.searchTerm || this.filterStatus !== 'all'
              ? 'Try adjusting your search or filter criteria.'
              : 'Connect to Acumatica to load sales order data.'}
          </p>
        </div>
      `;
    }

    // Calculate pagination
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredOrders.length);
    const pageOrders = this.filteredOrders.slice(startIndex, endIndex);

    return `
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              ${this.renderTableHeader('OrderNbr', 'Order #')}
              ${this.renderTableHeader('Status', 'Status')}
              ${this.renderTableHeader('OrderTotal', 'Total')}
              ${this.renderTableHeader('OrderedQty', 'Qty')}
              ${this.renderTableHeader('LineItemCount', 'Items')}
              ${this.renderTableHeader('CreatedDate', 'Created')}
              ${this.renderTableHeader('ShippingDate', 'Ship Date')}
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            ${pageOrders.map(order => this.renderTableRow(order)).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  renderTableHeader(field, label) {
    const isActive = this.sortField === field;
    const direction = isActive ? this.sortDirection : 'none';
    const nextDirection = isActive && direction === 'asc' ? 'desc' : 'asc';

    return `
      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
          data-sort-field="${field}" data-sort-direction="${nextDirection}">
        <div class="flex items-center space-x-1">
          <span>${label}</span>
          <div class="flex flex-col">
            <i class="fas fa-caret-up text-xs ${isActive && direction === 'asc' ? 'text-blue-600' : 'text-gray-300'}"></i>
            <i class="fas fa-caret-down text-xs ${isActive && direction === 'desc' ? 'text-blue-600' : 'text-gray-300'}" style="margin-top: -2px;"></i>
          </div>
        </div>
      </th>
    `;
  }

  renderTableRow(order) {
    return `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              ${this.escapeHtml(order.OrderNbr)}
            </div>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(order.Status)}">
            ${this.escapeHtml(order.Status)}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
          ${this.formatCurrency(order.OrderTotal)}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
          ${order.OrderedQty.toFixed(2)}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
          <div class="flex items-center space-x-2">
            <span>${order.LineItemCount}</span>
            ${order.TotalBOMItems > 0 ? `
              <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-200">
                <i class="fas fa-cogs mr-1"></i>
                ${order.TotalBOMItems} BOM
              </span>
            ` : ''}
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatDate(order.CreatedDate)}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatDate(order.ShippingDate)}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <button
            class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
            data-order-id="${order.id}"
            data-action="view-details"
          >
            <i class="fas fa-eye mr-1"></i>
            View Details
          </button>
        </td>
      </tr>
    `;
  }

  renderPagination() {
    if (this.totalPages <= 1) return '';

    const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
    const endItem = Math.min(this.currentPage * this.itemsPerPage, this.filteredOrders.length);

    return `
      <div class="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
        <div class="flex items-center justify-between">
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              id="prevPageMobile"
              ${this.currentPage === 1 ? 'disabled' : ''}
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              id="nextPageMobile"
              ${this.currentPage === this.totalPages ? 'disabled' : ''}
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700 dark:text-gray-300">
                Showing <span class="font-medium">${startItem}</span> to <span class="font-medium">${endItem}</span> of{' '}
                <span class="font-medium">${this.filteredOrders.length}</span> results
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  id="firstPage"
                  ${this.currentPage === 1 ? 'disabled' : ''}
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <i class="fas fa-angle-double-left"></i>
                </button>
                <button
                  id="prevPage"
                  ${this.currentPage === 1 ? 'disabled' : ''}
                  class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <i class="fas fa-angle-left"></i>
                </button>

                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-200">
                  Page ${this.currentPage} of ${this.totalPages}
                </span>

                <button
                  id="nextPage"
                  ${this.currentPage === this.totalPages ? 'disabled' : ''}
                  class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <i class="fas fa-angle-right"></i>
                </button>
                <button
                  id="lastPage"
                  ${this.currentPage === this.totalPages ? 'disabled' : ''}
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <i class="fas fa-angle-double-right"></i>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  // Utility methods
  getDataSourceInfo() {
    if (this.dataSource === 'acumatica') {
      return `Live data${this.lastSyncTime ? ` • Last sync: ${this.lastSyncTime}` : ''}`;
    } else if (this.dataSource === 'indexedDB') {
      return `Offline data${this.lastSyncTime ? ` • Last sync: ${this.lastSyncTime}` : ''}`;
    } else {
      return 'No data available';
    }
  }

  getStatusClass(status) {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'on hold':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'back order':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'shipping':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'completed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }

  formatCurrency(amount) {
    if (typeof amount !== 'number') return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatDate(dateObj) {
    if (!dateObj || !dateObj.year) return 'N/A';

    try {
      const date = new Date(dateObj.year, dateObj.month - 1, dateObj.day);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', dateObj, error);
      return 'Invalid Date';
    }
  }

  escapeHtml(text) {
    if (typeof text !== 'string') return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  // Debounce utility
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  setupEventListeners() {
    // Add a small delay to ensure DOM is fully rendered
    setTimeout(() => {
      // Search input
      const searchInput = document.getElementById('sales-search');
      if (searchInput) {
        searchInput.addEventListener('input', this.debounce(() => {
          this.searchTerm = searchInput.value.trim();
          this.currentPage = 1;
          this.applyFilters();
        }, 300));
      }

      // Clear search
      const clearSearchBtn = document.getElementById('clear-search');
      if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', () => {
          this.searchTerm = '';
          if (searchInput) searchInput.value = '';
          this.currentPage = 1;
          this.applyFilters();
        });
      }

      // Status filter
      const statusFilter = document.getElementById('order-status-filter');
      if (statusFilter) {
        statusFilter.addEventListener('change', () => {
          this.filterStatus = statusFilter.value;
          this.currentPage = 1;
          this.applyFilters();
        });
      }

      // Refresh button
      const refreshButton = document.getElementById('refresh-button');
      if (refreshButton) {
        refreshButton.addEventListener('click', () => {
          console.log('Refresh button clicked');
          this.loadData(true);
        });
      }

      // Export button
      const exportButton = document.getElementById('export-button');
      if (exportButton) {
        // Remove any existing event listeners first to prevent duplicates
        exportButton.replaceWith(exportButton.cloneNode(true));
        // Get the fresh reference after cloning
        const freshExportButton = document.getElementById('export-button');
        if (freshExportButton) {
          freshExportButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Export button clicked');
            this.exportToCSV();
          });
        }
      }

      // Settings button
      const settingsButton = document.getElementById('settings-button');
      if (settingsButton) {
        settingsButton.addEventListener('click', () => {
          console.log('Settings button clicked');
          this.showSettingsModal();
        });
      }

      // Sort headers
      const sortHeaders = document.querySelectorAll('th[data-sort]');
      sortHeaders.forEach(header => {
        header.addEventListener('click', () => {
          const field = header.getAttribute('data-sort');
          if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
          } else {
            this.sortField = field;
            this.sortDirection = 'asc';
          }
          this.applyFilters();
        });
      });

      // Pagination event handlers
      const firstPageBtn = document.getElementById('first-page');
      if (firstPageBtn) {
        firstPageBtn.addEventListener('click', () => {
          console.log('First page button clicked');
          if (this.currentPage > 1) {
            this.currentPage = 1;
            this.render();
          }
        });
      }

      const prevPageBtn = document.getElementById('prev-page');
      if (prevPageBtn) {
        prevPageBtn.addEventListener('click', () => {
          console.log('Previous page button clicked');
          if (this.currentPage > 1) {
            this.currentPage--;
            this.render();
          }
        });
      }

      const nextPageBtn = document.getElementById('next-page');
      if (nextPageBtn) {
        nextPageBtn.addEventListener('click', () => {
          console.log('Next page button clicked');
          if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.render();
          }
        });
      }

      const lastPageBtn = document.getElementById('last-page');
      if (lastPageBtn) {
        lastPageBtn.addEventListener('click', () => {
          console.log('Last page button clicked');
          if (this.currentPage < this.totalPages) {
            this.currentPage = this.totalPages;
            this.render();
          }
        });
      }

      // View order buttons
      const viewButtons = document.querySelectorAll('.view-order');
      viewButtons.forEach(button => {
        button.addEventListener('click', (event) => {
          event.preventDefault(); // Prevent default action
          event.stopPropagation(); // Stop event bubbling
          console.log('View order button clicked');
          const orderId = button.getAttribute('data-id');
          this.viewOrderDetails(orderId);
        });
      });

      console.log('All event listeners have been set up');
    }, 50); // Small delay to ensure DOM is ready
  }

  // Utility methods
  getStatusClass(status) {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'on hold':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'back order':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'shipping':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'completed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }

  formatCurrency(amount) {
    if (typeof amount !== 'number') return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatDate(dateObj) {
    if (!dateObj || !dateObj.year) return 'N/A';

    try {
      const date = new Date(dateObj.year, dateObj.month - 1, dateObj.day);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', dateObj, error);
      return 'Invalid Date';
    }
  }

  escapeHtml(text) {
    if (typeof text !== 'string') return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  // Debounce utility
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  viewOrderDetails(orderId) {
    const order = this.salesOrders.find(o => o.id === orderId);
    if (!order) {
      console.error('Order not found:', orderId);
      return;
    }

    console.log('Viewing order details for:', order.OrderNbr);
    this.showOrderDetails(order);
  }

  showOrderDetails(order) {
    if (!order) {
      console.error('Order not provided');
      return;
    }

    const modalHtml = `
      <div id="orderDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
          <div class="flex items-center justify-between pb-3 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Sales Order Details - ${this.escapeHtml(order.OrderNbr)}
            </h3>
            <button id="closeOrderModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <i class="fas fa-times text-xl"></i>
            </button>
          </div>

          <div class="mt-4">
            <!-- Order Summary -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Order Information</h4>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-300">Order #:</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(order.OrderNbr)}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-300">Status:</span>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(order.Status)}">
                      ${this.escapeHtml(order.Status)}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-300">Type:</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(order.OrderType)}</span>
                  </div>
                </div>
              </div>

              <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Quantities & Totals</h4>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-300">Total Qty:</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">${order.OrderedQty.toFixed(2)}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-300">Line Items:</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">${order.LineItemCount}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-300">Order Total:</span>
                    <span class="text-sm font-bold text-gray-900 dark:text-white">${this.formatCurrency(order.OrderTotal)}</span>
                  </div>
                </div>
              </div>

              <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Dates</h4>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-300">Created:</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">${this.formatDate(order.CreatedDate)}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-300">Ship Date:</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">${this.formatDate(order.ShippingDate)}</span>
                  </div>
                  ${order.TotalBOMItems > 0 ? `
                    <div class="flex justify-between">
                      <span class="text-sm text-gray-600 dark:text-gray-300">BOM Items:</span>
                      <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-200">
                        <i class="fas fa-cogs mr-1"></i>
                        ${order.TotalBOMItems}
                      </span>
                    </div>
                  ` : ''}
                </div>
              </div>
            </div>

            <!-- Line Items Table -->
            <div class="mb-4">
              <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Line Items</h4>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Item</th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Description</th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Qty</th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Unit Price</th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Extended</th>
                      <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Production</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    ${order.LineItems.map(item => `
                      <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                          <div class="flex items-center space-x-2">
                            <span>${this.escapeHtml(item.InventoryID)}</span>
                            ${item.IsProductionNumber ? `
                              <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-200">
                                <i class="fas fa-industry mr-1"></i>
                                Production #
                              </span>
                            ` : item.OriginalInventoryID ? `
                              <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-200">
                                <i class="fas fa-box mr-1"></i>
                                Inventory
                              </span>
                            ` : ''}
                          </div>
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
                          <div class="max-w-xs truncate" title="${this.escapeHtml(item.LineDescription)}">
                            ${this.escapeHtml(item.LineDescription)}
                          </div>
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
                          ${item.Quantity.toFixed(2)} ${this.escapeHtml(item.UOM)}
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
                          ${this.formatCurrency(item.UnitPrice)}
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
                          ${this.formatCurrency(item.ExtAmount)}
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
                          ${item.BOMCount > 0 ? `
                            <div class="space-y-1">
                              ${item.BOMItems.map(bom => `
                                <div class="flex items-center space-x-2">
                                  ${bom.ProductionNbr ? `
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-200">
                                      <i class="fas fa-industry mr-1"></i>
                                      ${this.escapeHtml(bom.ProductionNbr)}
                                    </span>
                                  ` : `
                                    <span class="text-xs text-gray-500 dark:text-gray-400">No Production #</span>
                                  `}
                                </div>
                              `).join('')}
                            </div>
                          ` : `
                            <span class="text-xs text-gray-500 dark:text-gray-400">No BOM</span>
                          `}
                        </td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
            <button id="closeOrderModalBtn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
              Close
            </button>
          </div>
        </div>
      </div>
    `;

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Add event listeners for closing modal with a small delay to ensure DOM is ready
    setTimeout(() => {
      const closeModal = () => {
        const modal = document.getElementById('orderDetailsModal');
        if (modal) {
          modal.remove();
        }
      };

      // Close button in header
      const closeHeaderBtn = document.getElementById('closeOrderModal');
      if (closeHeaderBtn) {
        closeHeaderBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('Close header button clicked');
          closeModal();
        });
      }

      // Close button in footer
      const closeFooterBtn = document.getElementById('closeOrderModalBtn');
      if (closeFooterBtn) {
        closeFooterBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('Close footer button clicked');
          closeModal();
        });
      }

      // Close on backdrop click
      const modalElement = document.getElementById('orderDetailsModal');
      if (modalElement) {
        modalElement.addEventListener('click', (e) => {
          if (e.target.id === 'orderDetailsModal') {
            console.log('Backdrop clicked');
            closeModal();
          }
        });
      }

      // Close on Escape key
      const handleEscape = (e) => {
        if (e.key === 'Escape') {
          console.log('Escape key pressed');
          closeModal();
          document.removeEventListener('keydown', handleEscape);
        }
      };
      document.addEventListener('keydown', handleEscape);

      console.log('Modal event listeners set up');
    }, 100);
  }

  exportToCSV() {
    if (this.filteredOrders.length === 0) {
      this.showError('No data to export');
      return;
    }

    try {
      // Prepare CSV headers
      const headers = [
        'Order Number',
        'Status',
        'Order Type',
        'Order Total',
        'Ordered Qty',
        'Line Items',
        'BOM Items',
        'Created Date',
        'Shipping Date',
        'Line Item Details'
      ];

      // Prepare CSV rows
      const rows = this.filteredOrders.map(order => {
        const lineItemDetails = order.LineItems.map(item =>
          `${item.InventoryID || 'N/A'}: ${item.LineDescription} (Qty: ${item.Quantity}, Price: ${item.UnitPrice})`
        ).join('; ');

        return [
          order.OrderNbr,
          order.Status,
          order.OrderType,
          order.OrderTotal,
          order.OrderedQty,
          order.LineItemCount,
          order.TotalBOMItems,
          this.formatDate(order.CreatedDate),
          this.formatDate(order.ShippingDate),
          lineItemDetails
        ];
      });

      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `sales_orders_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`Exported ${this.filteredOrders.length} sales orders to CSV`);
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      this.showError('Failed to export data: ' + error.message);
    }
  }

  showSettingsModal() {
    const modalHtml = `
      <div id="salesOrderSettingsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
          <div class="flex items-center justify-between pb-3 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Sales Order Settings</h3>
            <button id="closeSettingsModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <i class="fas fa-times text-xl"></i>
            </button>
          </div>

          <div class="mt-4 space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Items per page
              </label>
              <select id="itemsPerPageSetting" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                <option value="5" ${this.itemsPerPage === 5 ? 'selected' : ''}>5</option>
                <option value="10" ${this.itemsPerPage === 10 ? 'selected' : ''}>10</option>
                <option value="25" ${this.itemsPerPage === 25 ? 'selected' : ''}>25</option>
                <option value="50" ${this.itemsPerPage === 50 ? 'selected' : ''}>50</option>
                <option value="100" ${this.itemsPerPage === 100 ? 'selected' : ''}>100</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Default sort field
              </label>
              <select id="sortFieldSetting" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                <option value="OrderNbr" ${this.sortField === 'OrderNbr' ? 'selected' : ''}>Order Number</option>
                <option value="Status" ${this.sortField === 'Status' ? 'selected' : ''}>Status</option>
                <option value="OrderTotal" ${this.sortField === 'OrderTotal' ? 'selected' : ''}>Order Total</option>
                <option value="CreatedDate" ${this.sortField === 'CreatedDate' ? 'selected' : ''}>Created Date</option>
                <option value="ShippingDate" ${this.sortField === 'ShippingDate' ? 'selected' : ''}>Shipping Date</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Default sort direction
              </label>
              <select id="sortDirectionSetting" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                <option value="asc" ${this.sortDirection === 'asc' ? 'selected' : ''}>Ascending</option>
                <option value="desc" ${this.sortDirection === 'desc' ? 'selected' : ''}>Descending</option>
              </select>
            </div>

            ${this.lastSyncTime ? `
              <div class="pt-2 border-t border-gray-200 dark:border-gray-700">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Last sync: ${this.lastSyncTime}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Data source: ${this.dataSource === 'acumatica' ? 'Live (Acumatica)' : 'Offline (IndexedDB)'}
                </p>
              </div>
            ` : ''}
          </div>

          <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700 mt-6">
            <button id="cancelSettings" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
              Cancel
            </button>
            <button id="saveSettings" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
              Save Settings
            </button>
          </div>
        </div>
      </div>
    `;

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Add event listeners
    const closeModal = () => {
      const modal = document.getElementById('salesOrderSettingsModal');
      if (modal) {
        modal.remove();
      }
    };

    const saveSettings = async () => {
      const itemsPerPage = parseInt(document.getElementById('itemsPerPageSetting').value);
      const sortField = document.getElementById('sortFieldSetting').value;
      const sortDirection = document.getElementById('sortDirectionSetting').value;

      // Update component settings
      this.itemsPerPage = itemsPerPage;
      this.sortField = sortField;
      this.sortDirection = sortDirection;

      // Save to IndexedDB
      await this.saveSettings({
        itemsPerPage,
        sortField,
        sortDirection,
        lastSyncTime: this.lastSyncTime
      });

      // Re-apply filters and render
      this.applyFilters();
      this.render();

      closeModal();
    };

    document.getElementById('closeSettingsModal').addEventListener('click', closeModal);
    document.getElementById('cancelSettings').addEventListener('click', closeModal);
    document.getElementById('saveSettings').addEventListener('click', saveSettings);

    // Close on backdrop click
    document.getElementById('salesOrderSettingsModal').addEventListener('click', (e) => {
      if (e.target.id === 'salesOrderSettingsModal') {
        closeModal();
      }
    });
  }

  showError(message) {
    // Remove any existing error messages
    const existingError = document.getElementById('salesOrderError');
    if (existingError) {
      existingError.remove();
    }

    const errorHtml = `
      <div id="salesOrderError" class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative dark:bg-red-900 dark:border-red-700 dark:text-red-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="ml-3">
            <span class="block sm:inline">${this.escapeHtml(message)}</span>
          </div>
          <div class="ml-auto">
            <button id="dismissError" class="text-red-500 hover:text-red-700 dark:text-red-300 dark:hover:text-red-100">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>
    `;

    // Insert error message at the top of the container
    this.container.insertAdjacentHTML('afterbegin', errorHtml);

    // Add dismiss functionality
    const dismissBtn = document.getElementById('dismissError');
    if (dismissBtn) {
      dismissBtn.addEventListener('click', () => {
        const errorDiv = document.getElementById('salesOrderError');
        if (errorDiv) {
          errorDiv.remove();
        }
      });
    }

    // Auto-dismiss after 10 seconds
    setTimeout(() => {
      const errorDiv = document.getElementById('salesOrderError');
      if (errorDiv) {
        errorDiv.remove();
      }
    }, 10000);
  }

  showError(message) {
    console.error("Sales Order Error:", message);

    if (this.container) {
      // Remove any existing error messages
      const existingError = document.getElementById('sales-error-message');
      if (existingError) {
        existingError.remove();
      }

      // Create error element
      const errorElement = document.createElement('div');
      errorElement.id = 'sales-error-message';
      errorElement.className = 'bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4 dark:bg-red-900 dark:border-red-700 dark:text-red-200';
      errorElement.innerHTML = `
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-3">
            <span class="block sm:inline">${this.escapeHtml(message)}</span>
          </div>
          <div class="ml-auto">
            <button id="dismiss-error" class="text-red-500 hover:text-red-700 dark:text-red-300 dark:hover:text-red-100">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      `;

      // Insert at the beginning of the container
      this.container.insertBefore(errorElement, this.container.firstChild);

      // Add event listener to dismiss button
      const dismissButton = document.getElementById('dismiss-error');
      if (dismissButton) {
        dismissButton.addEventListener('click', () => {
          const errorMsg = document.getElementById('sales-error-message');
          if (errorMsg) {
            errorMsg.remove();
          }
        });
      }
    } else {
      console.error("Container not available to show error:", message);
    }
  }
}